// src/views/yunying/guild-store/data/prize.data.ts
import { BasicColumn, FormSchema } from '@/components/Table';
import { UploadFileParams } from '#/axios';
import { AxiosProgressEvent } from 'axios';
import { getObsToken, uploadUseImg } from '@/api/exchange';
import { uploadApi } from '@/api/sys/upload';
import { h } from 'vue';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import TextTooltip from '@/components/TextTooltip/index.vue';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.tz.setDefault('Asia/Shanghai');

const redTextVNode = (text: string) =>
  h('div', {
    innerHTML: text,
    style: {
      color: 'red',
      fontSize: '14px',
    },
  });

const _uploadApi = async (
  params: UploadFileParams,
  onUploadProgress: (progressEvent: AxiosProgressEvent) => void,
) => {
  const { token } = await getObsToken();
  const res = await uploadApi(params, onUploadProgress, token as string);
  return res;
};
export function getPrizeColumns(): BasicColumn[] {
  return [
    {
      title: 'ID',
      dataIndex: 'prizeId',
      width: 80,
      fixed: 'left',
    },
    {
      title: '奖品名称',
      dataIndex: 'prizeName',
      width: 150,
      fixed: 'left',
      customRender: ({ text }) =>
        h(TextTooltip, {
          text,
          maxWidth: 180,
          maxLines: 2,
        }),
    },
    {
      title: '奖品图片',
      dataIndex: 'prizeImageUrl',
      width: 100,
    },
    {
      title: '展示权重',
      dataIndex: 'displayWeight',
      width: 100,
    },
    {
      title: '展示时间',
      dataIndex: 'displayStartTime',
      format(_, record) {
        if (!record.displayStartTime || !record.displayEndTime) return '';
        return `${dayjs.unix(record.displayStartTime).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')} - ${dayjs.unix(record.displayEndTime).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')}`;
      },
      ellipsis: false,
      width: 400,
    },
    {
      title: '兑换条件',
      dataIndex: 'redemptionPoints',
      width: 120,
    },
    {
      title: '单个公会兑换限制',
      dataIndex: 'guildRedemptionLimit',
      width: 150,
    },
    {
      title: '全站兑换限制',
      dataIndex: 'siteRedemptionLimit',
      width: 130,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      width: 200,
      ellipsis: true,
      customRender: ({ text }) =>
        h(TextTooltip, {
          text,
          maxWidth: 180,
          maxLines: 2,
        }),
    },
  ];
}

// 奖品配置表单Schema
export const prizeFormSchema: FormSchema[] = [
  {
    field: 'prizeName',
    label: '奖品名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入奖品名称',
    },
    rules: [
      {
        required: true,
        message: '请输入奖品名称',
      },
    ],
  },
  {
    field: 'prizeImageUrl',
    label: '奖品图片',
    component: 'ImageUpload',
    componentProps: {
      api: _uploadApi, // 需要根据项目实际情况配置上传接口
      maxNumber: 1,
      accept: ['image/*'],
      maxSize: 20,
      resultField: 'data.location',
    },
    rules: [
      {
        required: true,
        message: '请上传奖品图片',
        // 自定义验证器，处理数组格式的值
        // validator: (_, value) => {
        //   if (!value) {
        //     return Promise.reject(new Error('请上传奖品图片'));
        //   }
        //   // 数组格式且有内容 - 验证通过
        //   if (Array.isArray(value) && value.length > 0) {
        //     return Promise.resolve();
        //   }
        //   // 字符串格式且不为空 - 验证通过
        //   if (typeof value === 'string' && value.trim()) {
        //     return Promise.resolve();
        //   }
        //   console.log(12312);

        //   return Promise.reject(new Error('请上传奖品图片'));
        // },
      },
    ],
  },
  {
    field: 'displayWeight',
    label: '展示权重',
    component: 'InputNumber',
    componentProps: {
      placeholder: '权重越大，排在越前',
      min: 0,
    },
    suffix: redTextVNode('权重越大，排在越前'),
    rules: [
      {
        required: true,
        message: '请输入展示权重',
      },
    ],
  },
  {
    field: '[displayStartTime, displayEndTime]',
    label: '展示时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['开始日期', '结束日期'],
      showTime: {
        defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
      },
      // 自定义值转换
      valueFormat: 'X', // Unix 时间戳格式
    },
    // 自定义默认值
    defaultValue: () => [dayjs().startOf('day'), dayjs().endOf('day')],
    labelWidth: 110,
    colProps: {
      span: 24,
    },
    required: true,
    rules: [
      {
        required: true,
        message: '请选择展示时间',
      },
    ],
  },
  {
    field: 'redemptionPoints',
    label: '兑换条件',
    component: 'InputNumber',
    componentProps: {
      placeholder: '单个奖品所需积分数（仅支持正数）',
      max: 999999,
      min: 1,
    },
    suffix: redTextVNode('单个奖品所需积分数（仅支持正数）'),
    rules: [
      {
        required: true,
        message: '请输入兑换条件',
      },
      {
        pattern: /^[1-9]\d*$/,
        message: '请输入正整数',
      },
    ],
  },
  {
    field: 'guildRedemptionLimit',
    label: '单个公会兑换限制',
    component: 'InputNumber',
    componentProps: {
      min: -1,
      max: 999999,
      placeholder: '请输入限制数量',
    },
    suffix: redTextVNode('配置为-1时,购买不限制数量;活动页面不展示文案'),
    rules: [
      {
        required: true,
        message: '请输入单个公会兑换限制',
      },
    ],
  },
  {
    field: 'siteRedemptionLimit',
    label: '全站兑换限制',
    component: 'InputNumber',
    componentProps: {
      min: -1,
      max: 999999,
      placeholder: '请输入限制数量',
    },
    suffix: redTextVNode('配置为-1时,购买不限制数量;活动页面不展示文案'),
    rules: [
      {
        required: true,
        message: '请输入全站兑换限制',
      },
    ],
  },
  {
    field: 'remarks',
    label: '备注文案',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '兑换须知或活动规则等信息',
      rows: 4,
    },
    suffix: redTextVNode('不配置则活动页面不展示文案'),
  },
];
