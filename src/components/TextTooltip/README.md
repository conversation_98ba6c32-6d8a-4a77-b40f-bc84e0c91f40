# TextTooltip 智能文本提示组件

一个智能的文本溢出检测和提示组件，能够自动检测文本是否超出容器，并在需要时显示 tooltip。

## 功能特性

- ✅ 自动检测文本溢出（单行/多行）
- ✅ 智能显示/隐藏 tooltip
- ✅ 响应式尺寸变化监听
- ✅ 防抖优化性能
- ✅ 完整的 TypeScript 支持
- ✅ 可访问性友好
- ✅ 自定义样式支持

## 基础用法

### 单行文本溢出

```vue
<template>
  <div style="width: 200px;">
    <TextTooltip 
      text="这是一段很长的文本内容，可能会超出容器宽度显示省略号"
      :max-width="200"
    />
  </div>
</template>

<script setup>
import TextTooltip from '@/components/TextTooltip/index.vue'
</script>
```

### 多行文本溢出

```vue
<template>
  <div style="width: 300px;">
    <TextTooltip 
      text="这是一段很长的文本内容，支持多行显示。当内容超过指定行数时，会显示省略号并在鼠标悬停时显示完整内容的 tooltip。"
      :max-width="300"
      :max-lines="3"
      placement="bottom"
    />
  </div>
</template>
```

### 动态内容

```vue
<template>
  <div>
    <input v-model="dynamicText" placeholder="输入文本内容" />
    <div style="width: 200px; margin-top: 10px;">
      <TextTooltip 
        :text="dynamicText"
        :max-width="200"
        @overflow-change="handleOverflowChange"
      />
    </div>
    <p>当前状态: {{ isOverflow ? '文本溢出' : '文本正常显示' }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import TextTooltip from '@/components/TextTooltip/index.vue'

const dynamicText = ref('输入一些文本来测试溢出检测')
const isOverflow = ref(false)

function handleOverflowChange(overflow: boolean) {
  isOverflow.value = overflow
  console.log('溢出状态变化:', overflow)
}
</script>
```

### 自定义样式

```vue
<template>
  <TextTooltip 
    text="自定义样式的文本内容"
    :max-width="250"
    container-class="custom-container"
    text-class="custom-text"
    overlay-class-name="custom-tooltip"
  />
</template>

<style>
.custom-container {
  border: 1px solid #ddd;
  padding: 8px;
  border-radius: 4px;
}

.custom-text {
  color: #1890ff;
  font-weight: 500;
}

.custom-tooltip .ant-tooltip-inner {
  background: #1890ff;
  color: white;
}
</style>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| text | 要显示的文本内容 | string | - |
| maxWidth | 容器最大宽度 | number \| string | '100%' |
| maxLines | 最大显示行数 | number | 1 |
| placement | tooltip 显示位置 | TooltipPlacement | 'top' |
| disabled | 是否禁用 tooltip 功能 | boolean | false |
| containerClass | 自定义容器类名 | string | '' |
| textClass | 自定义文本类名 | string | '' |
| overlayClassName | tooltip 覆盖层类名 | string | '' |
| allowHtml | 是否允许 HTML 内容 | boolean | false |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| overflowChange | 溢出状态变化时触发 | (isOverflow: boolean) |

### Methods

通过 ref 可以调用以下方法：

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|-------|
| checkOverflow | 手动触发溢出检测 | - | Promise\<void\> |
| isShowingTooltip | 获取当前是否显示 tooltip | - | boolean |

### TooltipPlacement

```typescript
type TooltipPlacement = 
  | 'top' | 'bottom' | 'left' | 'right'
  | 'topLeft' | 'topRight' 
  | 'bottomLeft' | 'bottomRight'
  | 'leftTop' | 'leftBottom' 
  | 'rightTop' | 'rightBottom'
```

## 高级用法

### 响应式容器

```vue
<template>
  <div class="responsive-container">
    <TextTooltip 
      :text="longText"
      max-width="100%"
      :max-lines="2"
    />
  </div>
</template>

<style>
.responsive-container {
  width: 100%;
  max-width: 500px;
  resize: horizontal;
  overflow: auto;
  border: 1px solid #ccc;
  padding: 10px;
}
</style>
```

### 与表格结合使用

```vue
<template>
  <a-table :columns="columns" :data-source="data" />
</template>

<script setup>
import { h } from 'vue'
import TextTooltip from '@/components/TextTooltip/index.vue'

const columns = [
  {
    title: '描述',
    dataIndex: 'description',
    width: 200,
    customRender: ({ text }) => h(TextTooltip, {
      text,
      maxWidth: 180,
      maxLines: 2
    })
  }
]

const data = [
  {
    key: '1',
    description: '这是一段很长的描述文本，在表格中可能会溢出'
  }
]
</script>
```

## 性能优化

1. **防抖处理**: 组件内置防抖机制，避免频繁的 DOM 查询
2. **ResizeObserver**: 使用现代 API 监听尺寸变化，性能更好
3. **按需渲染**: 只在需要时渲染 tooltip 组件
4. **内存管理**: 组件卸载时自动清理监听器

## 浏览器兼容性

- Chrome >= 60
- Firefox >= 60  
- Safari >= 12
- Edge >= 79

## 注意事项

1. **HTML 内容**: 使用 `allowHtml` 时需要确保内容安全，避免 XSS 攻击
2. **性能考虑**: 大量使用时建议启用虚拟滚动
3. **样式继承**: 文本样式会影响溢出检测的准确性
4. **动态内容**: 内容变化时会自动重新检测溢出状态

## 故障排除

### 溢出检测不准确
- 检查容器是否有正确的宽度设置
- 确认文本样式（字体、行高等）已正确应用
- 尝试手动调用 `checkOverflow` 方法

### Tooltip 不显示
- 检查 `disabled` 属性是否为 false
- 确认文本确实发生了溢出
- 检查是否有 CSS 样式冲突

### 性能问题
- 减少同时使用的组件数量
- 考虑使用虚拟滚动
- 检查是否有不必要的响应式数据
