// src/components/TextTooltip/types.ts

export interface TextTooltipProps {
  /** 要显示的文本内容 */
  text: string
  /** 容器最大宽度，可以是数字（px）或字符串 */
  maxWidth?: number | string
  /** 最大显示行数，默认为 1（单行） */
  maxLines?: number
  /** tooltip 显示位置 */
  placement?: TooltipPlacement
  /** 是否禁用 tooltip 功能 */
  disabled?: boolean
  /** 自定义容器类名 */
  containerClass?: string
  /** 自定义文本类名 */
  textClass?: string
  /** tooltip 覆盖层类名 */
  overlayClassName?: string
  /** 是否允许 HTML 内容 */
  allowHtml?: boolean
}

export type TooltipPlacement = 
  | 'top'
  | 'bottom'
  | 'left'
  | 'right'
  | 'topLeft'
  | 'topRight'
  | 'bottomLeft'
  | 'bottomRight'
  | 'leftTop'
  | 'leftBottom'
  | 'rightTop'
  | 'rightBottom'

export interface TextTooltipEmits {
  /** 溢出状态变化事件 */
  overflowChange: [isOverflow: boolean]
}

export interface TextTooltipExpose {
  /** 手动触发溢出检测 */
  checkOverflow: () => Promise<void>
  /** 当前是否显示 tooltip */
  isShowingTooltip: boolean
}

export interface TextTooltipInstance {
  /** 手动触发溢出检测 */
  checkOverflow: () => Promise<void>
  /** 当前是否显示 tooltip */
  isShowingTooltip: boolean
}
