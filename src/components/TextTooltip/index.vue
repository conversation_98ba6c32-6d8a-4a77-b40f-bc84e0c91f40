<!-- src/components/TextTooltip/index.vue -->
<template>
  <div
    ref="containerRef"
    class="text-tooltip-container"
    :class="containerClass"
    :style="containerStyle"
  >
    <Tooltip
      v-if="!disabled && showTooltip"
      :title="text"
      :placement="placement"
      :overlay-class-name="overlayClassName"
    >
      <div
        ref="textRef"
        class="text-content"
        :class="textClass"
        :style="textStyle"
        v-html="displayText"
      ></div>
    </Tooltip>
    <div
      v-else
      ref="textRef"
      class="text-content"
      :class="textClass"
      :style="textStyle"
      v-html="displayText"
    ></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { useResizeObserver } from '@vueuse/core';

  // 组件属性定义
  export interface TextTooltipProps {
    /** 要显示的文本内容 */
    text: string;
    /** 容器最大宽度，可以是数字（px）或字符串 */
    maxWidth?: number | string;
    /** 最大显示行数，默认为 1（单行） */
    maxLines?: number;
    /** tooltip 显示位置 */
    placement?:
      | 'top'
      | 'bottom'
      | 'left'
      | 'right'
      | 'topLeft'
      | 'topRight'
      | 'bottomLeft'
      | 'bottomRight'
      | 'leftTop'
      | 'leftBottom'
      | 'rightTop'
      | 'rightBottom';
    /** 是否禁用 tooltip 功能 */
    disabled?: boolean;
    /** 自定义容器类名 */
    containerClass?: string;
    /** 自定义文本类名 */
    textClass?: string;
    /** tooltip 覆盖层类名 */
    overlayClassName?: string;
    /** 是否允许 HTML 内容 */
    allowHtml?: boolean;
  }

  const props = withDefaults(defineProps<TextTooltipProps>(), {
    maxWidth: '100%',
    maxLines: 1,
    placement: 'top',
    disabled: false,
    containerClass: '',
    textClass: '',
    overlayClassName: '',
    allowHtml: false,
  });

  // 事件定义
  const emit = defineEmits<{
    overflowChange: [isOverflow: boolean];
  }>();

  // 响应式引用
  const containerRef = ref<HTMLElement>();
  const textRef = ref<HTMLElement>();
  const showTooltip = ref(false);

  // 计算属性
  const containerStyle = computed(() => {
    const style: Record<string, any> = {};

    if (props.maxWidth) {
      if (typeof props.maxWidth === 'number') {
        style.maxWidth = `${props.maxWidth}px`;
      } else {
        style.maxWidth = props.maxWidth;
      }
    }

    return style;
  });

  const textStyle = computed(() => {
    const style: Record<string, any> = {};

    if (props.maxLines === 1) {
      // 单行省略
      style.overflow = 'hidden';
      style.textOverflow = 'ellipsis';
      style.whiteSpace = 'nowrap';
    } else if (props.maxLines > 1) {
      // 多行省略
      style.display = '-webkit-box';
      style.webkitBoxOrient = 'vertical';
      style.webkitLineClamp = props.maxLines;
      style.overflow = 'hidden';
      style.wordBreak = 'break-word';
    }

    return style;
  });

  const displayText = computed(() => {
    if (props.allowHtml) {
      return props.text;
    }
    return escapeHtml(props.text);
  });

  // 工具函数：转义 HTML
  function escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  // 检测文本是否溢出
  function checkOverflow(): boolean {
    if (!textRef.value || props.disabled) {
      return false;
    }

    const element = textRef.value;
    let isOverflow = false;

    if (props.maxLines === 1) {
      // 单行溢出检测
      isOverflow = element.scrollWidth > element.clientWidth;
    } else {
      // 多行溢出检测
      isOverflow = element.scrollHeight > element.clientHeight;
    }

    return isOverflow;
  }

  // 更新 tooltip 显示状态
  async function updateTooltipState() {
    if (props.disabled) {
      showTooltip.value = false;
      return;
    }

    await nextTick();

    const isOverflow = checkOverflow();

    if (showTooltip.value !== isOverflow) {
      showTooltip.value = isOverflow;
      emit('overflowChange', isOverflow);
    }
  }

  // 防抖函数
  function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;

    return (...args: Parameters<T>) => {
      if (timeout) {
        clearTimeout(timeout);
      }

      timeout = setTimeout(() => {
        func.apply(null, args);
      }, wait);
    };
  }

  // 防抖的更新函数
  const debouncedUpdate = debounce(updateTooltipState, 100);

  // 监听文本内容变化
  watch(() => props.text, debouncedUpdate, { immediate: true });

  // 监听最大宽度变化
  watch(() => props.maxWidth, debouncedUpdate);

  // 监听最大行数变化
  watch(() => props.maxLines, debouncedUpdate);

  // 监听禁用状态变化
  watch(
    () => props.disabled,
    (newVal) => {
      if (newVal) {
        showTooltip.value = false;
      } else {
        debouncedUpdate();
      }
    },
  );

  // 使用 ResizeObserver 监听容器尺寸变化
  let resizeObserver: ReturnType<typeof useResizeObserver> | null = null;

  onMounted(() => {
    if (containerRef.value) {
      resizeObserver = useResizeObserver(containerRef.value, debouncedUpdate);
    }

    // 初始检测
    updateTooltipState();
  });

  onUnmounted(() => {
    if (resizeObserver) {
      resizeObserver.stop();
    }
  });

  // 暴露方法给父组件
  defineExpose({
    /** 手动触发溢出检测 */
    checkOverflow: updateTooltipState,
    /** 当前是否显示 tooltip */
    isShowingTooltip: computed(() => showTooltip.value),
  });
</script>

<style lang="less" scoped>
  .text-tooltip-container {
    display: inline-block;
    width: 100%;

    .text-content {
      width: 100%;
      line-height: 1.5;
      word-wrap: break-word;

      // 确保文本内容可以正确测量
      &:empty::before {
        content: '\00a0'; // 不间断空格，防止空内容时高度塌陷
      }
    }
  }

  // 全局样式，用于 tooltip 内容
  :global(.text-tooltip-overlay) {
    .ant-tooltip-inner {
      max-width: 400px;
      word-wrap: break-word;
      white-space: pre-wrap;
      text-align: left;
    }
  }

  // 多行文本的特殊处理
  .text-content[style*='-webkit-line-clamp'] {
    // 确保多行省略在各种情况下都能正常工作
    line-height: 1.4;

    // 兼容性处理
    display: -webkit-box !important;
    -webkit-box-orient: vertical !important;
    overflow: hidden !important;
  }

  // 单行文本的特殊处理
  .text-content[style*='text-overflow: ellipsis'] {
    // 确保单行省略正常工作
    display: block;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  // 响应式处理
  @media (max-width: 768px) {
    .text-tooltip-container {
      .text-content {
        font-size: 14px;
      }
    }

    :global(.text-tooltip-overlay) {
      .ant-tooltip-inner {
        max-width: 280px;
        font-size: 14px;
      }
    }
  }

  // 高对比度模式支持
  @media (prefers-contrast: high) {
    .text-content {
      font-weight: 500;
    }
  }

  // 减少动画模式支持
  @media (prefers-reduced-motion: reduce) {
    :global(.text-tooltip-overlay) {
      .ant-tooltip {
        transition: none !important;
      }
    }
  }
</style>
