<!-- src/components/TextTooltip/demo.vue -->
<template>
  <div class="text-tooltip-demo">
    <h1>TextTooltip 组件演示</h1>
    
    <!-- 基础用法 -->
    <section class="demo-section">
      <h2>基础用法 - 单行文本</h2>
      <div class="demo-container" style="width: 200px;">
        <TextTooltip 
          text="这是一段很长的文本内容，可能会超出容器宽度显示省略号"
          :max-width="200"
        />
      </div>
    </section>

    <!-- 多行文本 -->
    <section class="demo-section">
      <h2>多行文本溢出</h2>
      <div class="demo-container" style="width: 300px;">
        <TextTooltip 
          text="这是一段很长的文本内容，支持多行显示。当内容超过指定行数时，会显示省略号并在鼠标悬停时显示完整内容的 tooltip。这个功能对于需要在有限空间内展示大量文本信息的场景非常有用。"
          :max-width="300"
          :max-lines="3"
          placement="bottom"
        />
      </div>
    </section>

    <!-- 动态内容测试 -->
    <section class="demo-section">
      <h2>动态内容测试</h2>
      <div class="demo-controls">
        <a-input 
          v-model:value="dynamicText" 
          placeholder="输入文本内容测试溢出检测"
          style="width: 300px; margin-bottom: 10px;"
        />
        <div>
          <label>最大宽度: </label>
          <a-slider 
            v-model:value="maxWidth" 
            :min="100" 
            :max="500" 
            style="width: 200px; display: inline-block; margin: 0 10px;"
          />
          <span>{{ maxWidth }}px</span>
        </div>
        <div style="margin-top: 10px;">
          <label>最大行数: </label>
          <a-radio-group v-model:value="maxLines">
            <a-radio :value="1">1行</a-radio>
            <a-radio :value="2">2行</a-radio>
            <a-radio :value="3">3行</a-radio>
            <a-radio :value="0">不限制</a-radio>
          </a-radio-group>
        </div>
      </div>
      <div class="demo-container" :style="{ width: maxWidth + 'px' }">
        <TextTooltip 
          :text="dynamicText"
          :max-width="maxWidth"
          :max-lines="maxLines || undefined"
          @overflow-change="handleOverflowChange"
        />
      </div>
      <div class="status-info">
        <a-tag :color="isOverflow ? 'red' : 'green'">
          {{ isOverflow ? '文本溢出' : '文本正常显示' }}
        </a-tag>
      </div>
    </section>

    <!-- 不同位置的 tooltip -->
    <section class="demo-section">
      <h2>Tooltip 位置</h2>
      <div class="placement-grid">
        <div 
          v-for="placement in placements" 
          :key="placement"
          class="placement-item"
        >
          <div class="placement-label">{{ placement }}</div>
          <div class="demo-container" style="width: 120px;">
            <TextTooltip 
              text="这是一段测试文本，用于演示不同位置的 tooltip 效果"
              :max-width="120"
              :placement="placement"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- 自定义样式 -->
    <section class="demo-section">
      <h2>自定义样式</h2>
      <div class="demo-container" style="width: 250px;">
        <TextTooltip 
          text="这是带有自定义样式的文本内容，包括边框、内边距和颜色"
          :max-width="250"
          container-class="custom-container"
          text-class="custom-text"
          overlay-class-name="custom-tooltip"
        />
      </div>
    </section>

    <!-- 禁用状态 -->
    <section class="demo-section">
      <h2>禁用状态</h2>
      <div class="demo-controls">
        <a-switch v-model:checked="tooltipDisabled" />
        <span style="margin-left: 10px;">禁用 Tooltip</span>
      </div>
      <div class="demo-container" style="width: 200px;">
        <TextTooltip 
          text="这段文本会溢出，但可以通过开关控制是否显示 tooltip"
          :max-width="200"
          :disabled="tooltipDisabled"
        />
      </div>
    </section>

    <!-- 表格中的应用 -->
    <section class="demo-section">
      <h2>表格中的应用</h2>
      <a-table :columns="tableColumns" :data-source="tableData" :pagination="false" />
    </section>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { Input, Slider, RadioGroup, Radio, Tag, Switch, Table } from 'ant-design-vue'
import TextTooltip from './index.vue'

// 动态内容测试
const dynamicText = ref('输入一些文本来测试溢出检测功能，可以输入很长的内容来观察效果')
const maxWidth = ref(250)
const maxLines = ref(1)
const isOverflow = ref(false)
const tooltipDisabled = ref(false)

// Tooltip 位置选项
const placements = [
  'top', 'topLeft', 'topRight',
  'bottom', 'bottomLeft', 'bottomRight',
  'left', 'leftTop', 'leftBottom',
  'right', 'rightTop', 'rightBottom'
]

// 溢出状态变化处理
function handleOverflowChange(overflow) {
  isOverflow.value = overflow
  console.log('溢出状态变化:', overflow)
}

// 表格数据和列配置
const tableColumns = [
  {
    title: '姓名',
    dataIndex: 'name',
    width: 100,
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 200,
    customRender: ({ text }) => h(TextTooltip, {
      text,
      maxWidth: 180,
      maxLines: 2
    })
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    customRender: ({ text }) => h(TextTooltip, {
      text,
      maxWidth: 130,
      maxLines: 1
    })
  }
]

const tableData = [
  {
    key: '1',
    name: '张三',
    description: '这是一段很长的描述文本，在表格中可能会溢出，需要使用 TextTooltip 组件来处理',
    remark: '重要客户，需要特别关注其需求变化'
  },
  {
    key: '2', 
    name: '李四',
    description: '短描述',
    remark: '普通客户'
  },
  {
    key: '3',
    name: '王五',
    description: '这是另一段长描述，用于测试多行文本的溢出效果和 tooltip 显示',
    remark: '新客户，刚刚注册，需要跟进了解具体需求'
  }
]
</script>

<style lang="less" scoped>
.text-tooltip-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    color: #1890ff;
    border-bottom: 2px solid #1890ff;
    padding-bottom: 10px;
  }

  .demo-section {
    margin-bottom: 40px;
    
    h2 {
      color: #333;
      margin-bottom: 20px;
    }
  }

  .demo-container {
    border: 1px dashed #d9d9d9;
    padding: 12px;
    margin: 10px 0;
    background: #fafafa;
    border-radius: 4px;
  }

  .demo-controls {
    margin-bottom: 15px;
    
    > div {
      margin-bottom: 10px;
    }
    
    label {
      display: inline-block;
      width: 80px;
      font-weight: 500;
    }
  }

  .status-info {
    margin-top: 10px;
  }

  .placement-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    
    .placement-item {
      text-align: center;
      
      .placement-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
      }
    }
  }
}

// 自定义样式示例
:deep(.custom-container) {
  border: 1px solid #1890ff;
  padding: 8px;
  border-radius: 4px;
  background: #f0f8ff;
}

:deep(.custom-text) {
  color: #1890ff;
  font-weight: 500;
}

:global(.custom-tooltip) {
  .ant-tooltip-inner {
    background: #1890ff;
    color: white;
    border-radius: 6px;
  }
  
  .ant-tooltip-arrow::before {
    background: #1890ff;
  }
}
</style>
